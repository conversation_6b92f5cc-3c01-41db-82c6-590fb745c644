/* eslint-disable no-console */
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontext, webkit } from '@playwright/test'

/**
 * WebKit browser factory with enhanced stability features
 * Implements retry logic and health checks to prevent browser crashes
 */
export class WebKitBrowserFactory {
  private static readonly MAX_RETRIES = 3

  private static readonly RETRY_DELAY = 2000

  private static readonly HEALTH_CHECK_TIMEOUT = 5000

  /**
   * Launch WebKit browser with retry logic
   */
  static async launch(options: Record<string, unknown> = {}): Promise<Browser> {
    // DEBUG: Log environment variables that could affect executable resolution
    console.log('🔍 [DEBUG] Browser launch environment variables:', {
      PLAYWRIGHT_BROWSERS_PATH: process.env.PLAYWRIGHT_BROWSERS_PATH,
      TIMEOUT_CMD: process.env.TIMEOUT_CMD,
      USE_TIMEOUT: process.env.USE_TIMEOUT,
      NODE_OPTIONS: process.env.NODE_OPTIONS,
      WEBKIT_DISABLE_COMPOSITING: process.env.WEBKIT_DISABLE_COMPOSITING,
    })

    // DEBUG: Log all environment variables that might contain executable paths
    const envKeys = Object.keys(process.env).filter(
      (key) =>
        key.toLowerCase().includes('path') ||
        key.toLowerCase().includes('exe') ||
        key.toLowerCase().includes('cmd')
    )
    console.log(
      '🔍 [DEBUG] Path/executable environment variables:',
      envKeys.reduce((acc, key) => ({ ...acc, [key]: process.env[key] }), {})
    )

    const defaultOptions = {
      timeout: 300000,
      slowMo: 500,
      headless: true,
      args: [
        // WebKit doesn't support many Chrome/Chromium flags
        // Only include WebKit-compatible args
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
      ],
      env: {
        ...process.env,
        WEBKIT_DISABLE_COMPOSITING: '1',
        WEBKIT_FORCE_COMPOSITING_MODE: '0',
        // Ensure PATH is properly set for WebKit executable discovery
        ...(process.env.PATH && { PATH: process.env.PATH }),
        // Additional stability environment variables
        WEBKIT_DISABLE_SANDBOX: '1',
        WEBKIT_DISABLE_SETUID_SANDBOX: '1',
      },
      // Ensure executablePath is properly handled
      ...(options.executablePath !== undefined &&
      typeof options.executablePath === 'string' &&
      options.executablePath.length > 0
        ? { executablePath: options.executablePath }
        : {}),
      // Additional WebKit-specific options for CI stability
      chromiumSandbox: false,
      handleSIGINT: false,
      handleSIGTERM: false,
      // Force single process for stability
      devtools: false,
      // Additional stability options
      ignoreDefaultArgs: ['--enable-automation'],
      ignoreHTTPSErrors: true,
    }

    const mergedOptions = { ...defaultOptions, ...options }

    // DEBUG: Log final launch options to identify potential issues
    console.log('🔍 [DEBUG] WebKit launch options:', {
      timeout: mergedOptions.timeout,
      headless: mergedOptions.headless,
      executablePath: (mergedOptions as Record<string, unknown>).executablePath,
      args: mergedOptions.args,
    })

    let lastError: Error | null = null

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        console.log(
          `🔧 Launching WebKit browser (attempt ${attempt}/${this.MAX_RETRIES})...`
        )

        // Add a delay between attempts to allow system recovery
        if (attempt > 1) {
          const delay = attempt * 2000 // 2s, 4s, 6s...
          console.log(`⏳ Waiting ${delay}ms before retry...`)
          // eslint-disable-next-line no-await-in-loop
          await new Promise(resolve => setTimeout(resolve, delay))
        }

        // Try to clean up any existing browser processes before launch
        if (attempt > 1) {
          console.log('🧹 Cleaning up existing browser processes...')
          try {
            // Kill any existing webkit processes
            if (process.platform === 'darwin') {
              // eslint-disable-next-line @typescript-eslint/no-var-requires, global-require
              const { execSync } = require('child_process')
              execSync('pkill -f "WebKit" || true', { stdio: 'ignore' })
              execSync('pkill -f "Safari" || true', { stdio: 'ignore' })
              execSync('pkill -f "Playwright" || true', { stdio: 'ignore' })
            }
          } catch (cleanupError) {
            console.log('⚠️ Process cleanup failed, continuing...', cleanupError)
          }
        }

        // eslint-disable-next-line no-await-in-loop
        const browser = await webkit.launch(mergedOptions)

        // Verify browser is healthy
        // eslint-disable-next-line no-await-in-loop
        const isHealthy = await this.checkBrowserHealth(browser)
        if (!isHealthy) {
          throw new Error('Browser health check failed')
        }

        console.log('✅ WebKit browser launched successfully')
        return browser
      } catch (error) {
        lastError = error as Error
        console.error(`❌ WebKit launch failed (attempt ${attempt}):`, error)

        // Enhanced error diagnostics
        console.error('🔍 [DEBUG] Error details:', {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          name: error instanceof Error ? error.name : undefined,
        })

        // Special handling for common WebKit errors
        if (error instanceof Error) {
          const errorMessage = error.message.toLowerCase()

          if (errorMessage.includes('exe.match is not a function')) {
            console.error(
              '🔍 [DEBUG] Detected "exe.match is not a function" error'
            )
            console.error(
              '🔍 [DEBUG] This suggests an issue with executable path resolution'
            )
          } else if (
            errorMessage.includes('browser has been closed') ||
            errorMessage.includes(
              'target page, context or browser has been closed'
            )
          ) {
            console.error('🔍 [DEBUG] Detected browser closure error')
            console.error(
              '🔍 [DEBUG] This suggests the browser crashed during launch'
            )
          } else if (errorMessage.includes('timeout')) {
            console.error(
              '🔍 [DEBUG] Detected timeout error during browser launch'
            )
          } else if (errorMessage.includes('executable')) {
            console.error('🔍 [DEBUG] Detected executable-related error')
          }

          console.error('🔍 [DEBUG] Current launch options:', {
            timeout: mergedOptions.timeout,
            headless: mergedOptions.headless,
            executablePath: (mergedOptions as Record<string, unknown>)
              .executablePath,
            args: mergedOptions.args?.length || 0,
            env: Object.keys(mergedOptions.env || {}).join(', '),
          })

          console.error('🔍 [DEBUG] Environment variables:', {
            PATH: process.env.PATH ? 'SET' : 'NOT SET',
            PLAYWRIGHT_BROWSERS_PATH:
              process.env.PLAYWRIGHT_BROWSERS_PATH || '(not set)',
            WEBKIT_DISABLE_COMPOSITING:
              process.env.WEBKIT_DISABLE_COMPOSITING || '(not set)',
            WEBKIT_FORCE_COMPOSITING_MODE:
              process.env.WEBKIT_FORCE_COMPOSITING_MODE || '(not set)',
            NODE_OPTIONS: process.env.NODE_OPTIONS || '(not set)',
          })
        }

        if (attempt < this.MAX_RETRIES) {
          console.log(`🔄 Retrying in ${this.RETRY_DELAY}ms...`)
          // eslint-disable-next-line no-await-in-loop
          await this.sleep(this.RETRY_DELAY)
        }
      }
    }

    throw new Error(
      `Failed to launch WebKit after ${this.MAX_RETRIES} attempts: ${lastError?.message || 'Unknown error'}`
    )
  }

  /**
   * Create browser context with stability options
   */
  static async createContext(
    browser: Browser,
    options: Record<string, unknown> = {}
  ): Promise<BrowserContext> {
    const defaultOptions = {
      viewport: { width: 390, height: 844 },
      reducedMotion: 'reduce' as const,
      forcedColors: 'none' as const,
      colorScheme: 'light' as const,
      serviceWorkers: 'block' as const,
      locale: 'en-US',
      permissions: [],
      timeout: 60000,
    }

    const mergedOptions = { ...defaultOptions, ...options }

    try {
      const context = await browser.newContext(mergedOptions)

      // Add event handlers for stability
      context.on('page', (page) => {
        page.on('crash', () => {
          console.error('⚠️ Page crashed!')
        })

        page.on('pageerror', (error) => {
          console.error('⚠️ Page error:', error)
        })
      })

      return context
    } catch (error) {
      console.error('❌ Failed to create browser context:', error)
      throw error
    }
  }

  /**
   * Check if browser is healthy and responsive
   */
  private static async checkBrowserHealth(browser: Browser): Promise<boolean> {
    try {
      const context = await browser.newContext({
        viewport: { width: 390, height: 844 },
      })

      const page = await context.newPage()

      // Set a timeout for the health check
      const healthCheckPromise = page.goto(
        'data:text/html,<html><body>Health Check</body></html>',
        {
          timeout: this.HEALTH_CHECK_TIMEOUT,
        }
      )

      await healthCheckPromise

      const content = await page.textContent('body')
      const isHealthy = content === 'Health Check'

      await page.close()
      await context.close()

      return isHealthy
    } catch (error) {
      console.error('❌ Browser health check failed:', error)
      return false
    }
  }

  /**
   * Gracefully close browser with cleanup
   */
  static async closeBrowser(browser: Browser): Promise<void> {
    try {
      if (browser && browser.isConnected()) {
        console.log('🧹 Closing WebKit browser...')
        await browser.close()
        console.log('✅ WebKit browser closed successfully')
      }
    } catch (error) {
      console.error('⚠️ Error closing browser:', error)
      // Force kill if graceful close fails
      try {
        if (browser) {
          await browser.close()
        }
      } catch {
        // Ignore secondary errors
      }
    }
  }

  /**
   * Sleep utility
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
}
