// eslint-disable-next-line import/no-extraneous-dependencies
import { defineConfig, devices } from '@playwright/test'
import { webkitProjectConfig } from './tests/e2e/webkit-config'

/**
 * Optimized CI-specific Playwright configuration
 * with enhanced WebKit stability and memory management
 */
export default defineConfig({
  testDir: './tests/e2e',
  /* Enable parallel execution for faster CI on GitHub-hosted runners */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: true,
  /* Reduced retries for faster feedback - WebKit needs fewer retries to avoid resource exhaustion */
  retries: 1,
  /* Use 1 worker for WebKit stability on self-hosted macOS runners */
  workers: 1,
  /* Support for sharding tests across multiple machines */
  /* Usage: --shard=1/4 --shard=2/4 etc. */
  /* Reporter configuration for CI */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list'],
    ['json', { outputFile: 'test-results/results.json' }],
  ],
  /* Reasonable timeout for E2E tests */
  timeout: 60000, // 1 minute per test (reduced from 3 minutes)
  /* Standard expect timeout */
  expect: {
    timeout: 15000, // 15 seconds for assertions (reduced from 45 seconds)
  },
  /* Output directory for test results */
  outputDir: 'test-results',
  /* Shared settings for all the projects below */
  use: {
    /* Base URL for local testing in CI */
    baseURL: 'http://localhost:3000',
    /* Minimal tracing for faster execution */
    trace: 'retain-on-failure',
    /* Screenshot on failure only */
    screenshot: 'only-on-failure',
    /* No video recording for faster execution */
    video: 'off',
    /* Standard action timeout */
    actionTimeout: 15000, // 15 seconds (reduced from 45 seconds)
    /* Standard navigation timeout */
    navigationTimeout: 30000, // 30 seconds (reduced from 90 seconds)
    /* Browser launch options for CI stability */
    launchOptions: {
      timeout: 300000, // 5 minutes for browser launch
      // WebKit doesn't support Chrome/Chromium specific flags
      args: [],
      // Force single process for WebKit stability
      chromiumSandbox: false,
      // Reasonable slowMo for stability without excessive delays
      slowMo: 250,
    },
    /* Context options for stability */
    contextOptions: {
      // Reduce memory usage
      viewport: { width: 390, height: 844 },
      // Disable unnecessary features
      javaScriptEnabled: true,
      bypassCSP: true,
      ignoreHTTPSErrors: true,
      // Reduce resource usage
      reducedMotion: 'reduce',
      // Disable service workers which can cause issues
      serviceWorkers: 'block',
      // Set explicit locale
      locale: 'en-US',
      // Disable permissions that might cause prompts
      permissions: [],
    },
    /* Global test setup */
    extraHTTPHeaders: {
      'X-Test-Context': 'playwright-ci',
      'Cache-Control': 'no-cache',
    },
  },

  /* Global setup and teardown */
  globalSetup: require.resolve('./tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.ts'),

  /* Configure projects for critical mobile paths */
  projects: [
    /* Mobile Safari - Primary target for critical tests */
    {
      ...webkitProjectConfig,
      name: 'Mobile Safari Critical',
      testMatch: /.*@critical.*\.spec\.ts$/,
      use: {
        ...webkitProjectConfig.use,
        // Enhanced WebKit stability settings
        launchOptions: {
          ...webkitProjectConfig.use.launchOptions,
          timeout: 300000,
          slowMo: 500, // Moderate slowMo for critical tests
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu',
          ],
          env: {
            ...process.env,
            WEBKIT_DISABLE_COMPOSITING: '1',
            WEBKIT_FORCE_COMPOSITING_MODE: '0',
            WEBKIT_DISABLE_SANDBOX: '1',
            WEBKIT_DISABLE_SETUID_SANDBOX: '1',
          },
          chromiumSandbox: false,
          handleSIGINT: false,
          handleSIGTERM: false,
          ignoreDefaultArgs: ['--enable-automation'],
        },
        contextOptions: {
          ...webkitProjectConfig.use.contextOptions,
          // Additional stability measures
          strictSelectors: false,
          ignoreHTTPSErrors: true,
          bypassCSP: true,
        },
        // Reasonable timeouts for WebKit
        actionTimeout: 20000,
        navigationTimeout: 40000,
      },
      retries: 3, // Reduced from 5 to prevent resource exhaustion
      workers: 1,
    },
    /* Mobile Chrome - Run for critical tests only as fallback */
    {
      name: 'Mobile Chrome Critical',
      use: {
        ...devices['Pixel 5'],
        viewport: { width: 393, height: 851 },
        hasTouch: true,
        isMobile: true,
        // Chrome-specific options for stability
        launchOptions: {
          timeout: 180000,
          args: [
            '--disable-dev-shm-usage',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
          ],
          slowMo: 500,
        },
        contextOptions: {
          viewport: { width: 393, height: 851 },
          reducedMotion: 'reduce',
          serviceWorkers: 'block',
        },
        actionTimeout: 30000,
        navigationTimeout: 60000,
      },
      testMatch: /.*@critical.*\.spec\.ts$/,
      retries: 3,
      workers: 1,
    },
    /* Mobile Safari - All tests (non-critical) */
    {
      ...webkitProjectConfig,
      name: 'Mobile Safari Full',
      testIgnore: /.*@critical.*\.spec\.ts$/,
      use: {
        ...webkitProjectConfig.use,
        // Standard WebKit settings for non-critical tests
        launchOptions: {
          ...webkitProjectConfig.use.launchOptions,
          slowMo: 250,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu',
          ],
          env: {
            ...process.env,
            WEBKIT_DISABLE_COMPOSITING: '1',
            WEBKIT_FORCE_COMPOSITING_MODE: '0',
            WEBKIT_DISABLE_SANDBOX: '1',
            WEBKIT_DISABLE_SETUID_SANDBOX: '1',
          },
          chromiumSandbox: false,
          handleSIGINT: false,
          handleSIGTERM: false,
          ignoreDefaultArgs: ['--enable-automation'],
        },
      },
      retries: 2, // Reduced retries for non-critical tests
      workers: 1,
    },
  ],

  /* Run local dev server before tests */
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 180000, // 3 minutes for server startup
    env: {
      NODE_OPTIONS: '--max_old_space_size=8192',
      NEXT_PUBLIC_DISABLE_OAUTH: 'true', // Disable OAuth in E2E tests
    },
  },
})
