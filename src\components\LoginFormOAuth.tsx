'use client'

import React from 'react'
import { useOAuth } from '@/hooks/useOAuth'
import { useHapticFeedback } from '@/hooks/useHapticFeedback'

interface LoginFormOAuthProps {
  isLoading: boolean
  returnUrl: string
  onSuccess?: () => void
  onError: (error: Error) => void
}

export function LoginFormOAuth({
  isLoading,
  returnUrl,
  onSuccess,
  onError,
}: LoginFormOAuthProps) {
  const oauth = useOAuth()
  const haptic = useHapticFeedback()

  if (!oauth.hasAnyProvider) {
    return null
  }

  const handleGoogleSignIn = async () => {
    haptic.light()
    await oauth.signInWithGoogle(
      () => {
        haptic.success()
        if (onSuccess) {
          onSuccess()
        } else {
          window.location.href = returnUrl
        }
      },
      (error) => {
        haptic.error()
        onError(error)
      }
    )
  }

  const handleAppleSignIn = async () => {
    haptic.light()
    await oauth.signInWithApple(
      () => {
        haptic.success()
        if (onSuccess) {
          onSuccess()
        } else {
          window.location.href = returnUrl
        }
      },
      (error) => {
        haptic.error()
        onError(error)
      }
    )
  }

  return (
    <>
      {/* Divider */}
      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-brand-primary/20" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-bg-secondary text-text-tertiary">
            Or continue with
          </span>
        </div>
      </div>

      {/* OAuth Buttons */}
      <div className="space-y-3">
        {/* Google Sign In */}
        {oauth.google.isConfigured && (
          <button
            type="button"
            onClick={handleGoogleSignIn}
            disabled={isLoading || oauth.isLoading === 'google'}
            className={`w-full h-12 px-4 border rounded-theme font-medium flex items-center justify-center transition-all duration-200 ${
              isLoading || oauth.isLoading === 'google'
                ? 'border-brand-primary/20 bg-bg-tertiary text-text-tertiary cursor-not-allowed opacity-60'
                : 'border-brand-primary/20 bg-bg-secondary text-text-primary hover:bg-bg-tertiary hover:border-brand-primary/40 active:scale-95 tap-highlight-none touch-manipulation'
            }`}
          >
            <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            {oauth.isLoading === 'google' ? (
              <div className="flex items-center">
                <div className="animate-spin h-5 w-5 mr-3 border-2 border-text-tertiary border-t-transparent rounded-full" />
                Signing in...
              </div>
            ) : (
              'Sign in with Google'
            )}
          </button>
        )}

        {/* Apple Sign In */}
        {oauth.apple.isConfigured && (
          <button
            type="button"
            onClick={handleAppleSignIn}
            disabled={isLoading || oauth.isLoading === 'apple'}
            className={`w-full h-12 px-4 border rounded-theme font-medium flex items-center justify-center transition-all duration-200 ${
              isLoading || oauth.isLoading === 'apple'
                ? 'border-brand-primary/20 bg-bg-tertiary text-text-tertiary cursor-not-allowed opacity-60'
                : 'border-brand-primary/20 bg-text-primary text-bg-primary hover:bg-text-primary/90 active:scale-95 tap-highlight-none touch-manipulation'
            }`}
          >
            <svg
              className="w-5 h-5 mr-3"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.54 4.09l.01-.01zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z" />
            </svg>
            {oauth.isLoading === 'apple' ? (
              <div className="flex items-center">
                <div className="animate-spin h-5 w-5 mr-3 border-2 border-white border-t-transparent rounded-full" />
                Signing in...
              </div>
            ) : (
              'Sign in with Apple'
            )}
          </button>
        )}
      </div>
    </>
  )
}
