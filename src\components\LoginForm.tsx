'use client'

import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useAuth } from '@/hooks/useAuth'
import { useRouter, useSearchParams } from 'next/navigation'
import { PerformanceMonitor, PerformanceMarks } from '@/utils/performance'
import { useHapticFeedback } from '@/hooks/useHapticFeedback'
import { sanitizeEmail, sanitizeInput } from '@/utils/sanitization'
import { Button } from '@/components/ui'
import { LoginFormOAuth } from './LoginFormOAuth'
import type { LoginFormData } from '@/types'

interface LoginFormProps {
  onSuccess?: () => void
}

export function LoginForm({ onSuccess }: LoginFormProps = {}) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login, isLoading, error } = useAuth()
  const haptic = useHapticFeedback()
  const [showPassword, setShowPassword] = useState(false)
  const [socialError, setSocialError] = useState<string | null>(null)

  const returnUrl = searchParams.get('from') || '/program'

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setFocus,
  } = useForm<LoginFormData>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
    },
  })

  // Auto-focus email input on mount
  useEffect(() => {
    setFocus('email')
  }, [setFocus])

  // Clear social error after timeout
  useEffect(() => {
    if (socialError) {
      const timer = setTimeout(() => setSocialError(null), 5000)
      return () => clearTimeout(timer)
    }
    // Return cleanup function that does nothing when socialError is null
    return () => {}
  }, [socialError])

  const onSubmit = async (data: LoginFormData) => {
    // Mark login start for performance tracking
    PerformanceMonitor.mark(PerformanceMarks.LOGIN_START)

    // Haptic feedback for mobile
    haptic.light()

    try {
      // Sanitize inputs before sending to API
      const sanitizedEmail = sanitizeEmail(data.email)
      const sanitizedPassword = sanitizeInput(data.password)

      await login({
        Username: sanitizedEmail,
        Password: sanitizedPassword,
      })

      // Mark login success
      PerformanceMonitor.mark(PerformanceMarks.LOGIN_SUCCESS)

      // Success haptic feedback
      haptic.success()

      // Small delay to ensure auth state is persisted
      await new Promise((resolve) => {
        setTimeout(resolve, 100)
      })

      // Call onSuccess callback if provided, otherwise redirect
      if (onSuccess) {
        onSuccess()
      } else {
        router.push(returnUrl)
      }
    } catch (err) {
      // Error haptic feedback
      haptic.error()
      // Error is handled by useAuth hook
    }
  }

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="space-y-6 animate-slide-up"
      data-testid="login-form"
    >
      {/* API Error Display */}
      {(error || socialError) && (
        <div
          role="alert"
          className="bg-red-500/10 border border-red-500/20 text-red-500 px-4 py-3 rounded-theme"
        >
          <p className="text-sm">{error || socialError}</p>
        </div>
      )}

      {/* Email Input */}
      <div>
        <label
          htmlFor="email"
          className="block text-sm sm:text-base font-medium text-text-secondary mb-2"
        >
          Email
        </label>
        <input
          id="email"
          type="email"
          inputMode="email"
          autoComplete="email"
          autoCapitalize="none"
          autoCorrect="off"
          aria-required="true"
          aria-invalid={!!errors.email}
          aria-describedby={errors.email ? 'email-error' : undefined}
          className={`w-full h-12 sm:h-14 px-4 border rounded-theme text-base sm:text-lg transition-all duration-200 bg-bg-tertiary text-text-primary ${
            errors.email ? 'border-red-500' : 'border-brand-primary/20'
          } focus:outline-none focus:ring-2 focus:ring-brand-primary/20 focus:border-brand-primary`}
          {...register('email', {
            required: 'Email is required',
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'Please enter a valid email',
            },
          })}
        />
        {errors.email && (
          <p
            id="email-error"
            role="alert"
            className="mt-2 text-sm text-red-500"
          >
            {errors.email.message}
          </p>
        )}
      </div>

      {/* Password Input */}
      <div>
        <label
          htmlFor="password"
          className="block text-sm sm:text-base font-medium text-text-secondary mb-2"
        >
          Password
        </label>
        <div className="relative">
          <input
            id="password"
            type={showPassword ? 'text' : 'password'}
            autoComplete="current-password"
            autoCapitalize="none"
            autoCorrect="off"
            aria-required="true"
            aria-invalid={!!errors.password}
            aria-describedby={errors.password ? 'password-error' : undefined}
            className={`w-full h-12 sm:h-14 px-4 pr-12 border rounded-theme text-base sm:text-lg transition-all duration-200 bg-bg-tertiary text-text-primary ${
              errors.password ? 'border-red-500' : 'border-brand-primary/20'
            } focus:outline-none focus:ring-2 focus:ring-brand-primary/20 focus:border-brand-primary`}
            {...register('password', {
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            })}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-text-tertiary hover:text-text-secondary transition-colors"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                />
              </svg>
            ) : (
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            )}
          </button>
        </div>
        {errors.password && (
          <p
            id="password-error"
            role="alert"
            className="mt-2 text-sm text-red-500"
          >
            {errors.password.message}
          </p>
        )}
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={!isValid || isLoading}
        loading={isLoading}
        fullWidth
        size="lg"
        variant="primary"
        goldGradient
        aria-label={isLoading ? 'Logging in' : 'Login'}
      >
        {isLoading ? 'Logging in...' : 'Login'}
      </Button>

      {/* OAuth Section */}
      <LoginFormOAuth
        isLoading={isLoading}
        returnUrl={returnUrl}
        onSuccess={onSuccess}
        onError={(error) => setSocialError(error.message)}
      />
    </form>
  )
}
